import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>} from "next/font/google";
import "./globals.css";

const nunito = Nunito({
  variable: "--font-nunito",
  subsets: ["latin"],
});

const geist = Roboto({
  variable: "--font-aclonica",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Pet Food",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">

      <body
        className={`${nunito.variable} ${geist.variable} bg-gray-50 antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
